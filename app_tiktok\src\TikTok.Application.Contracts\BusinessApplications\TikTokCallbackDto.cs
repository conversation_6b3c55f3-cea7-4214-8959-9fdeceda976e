using System;
using System.ComponentModel.DataAnnotations;

namespace TikTok.BusinessApplications
{
    /// <summary>
    /// DTO cho callback request từ TikTok
    /// </summary>
    public class TikTokCallbackRequestDto
    {
        /// <summary>
        /// Authorization code từ TikTok
        /// </summary>
        [Required]
        public string AuthCode { get; set; } = string.Empty;

        /// <summary>
        /// ID của Business Application (state parameter)
        /// </summary>
        [Required]
        public string BusinessApplicationId { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO cho callback response
    /// </summary>
    public class TikTokCallbackResponseDto
    {
        /// <summary>
        /// Xác định xem callback có thành công không
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Thông báo lỗi nếu có
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Access token đã được lưu (có thể null vì lý do bảo mật)
        /// </summary>
        public string? AccessToken { get; set; }

        /// <summary>
        /// Thời gian tạo access token
        /// </summary>
        public DateTime? AccessTokenCreatedAt { get; set; }
    }

    /// <summary>
    /// DTO cho response của redirect URI
    /// </summary>
    public class RedirectUriResponseDto
    {
        /// <summary>
        /// Redirect URI được cấu hình
        /// </summary>
        public string RedirectUri { get; set; } = string.Empty;
    }
}